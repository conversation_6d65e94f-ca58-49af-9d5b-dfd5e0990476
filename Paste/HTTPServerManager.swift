import Foundation
import Network

class HTTPServerManager: ObservableObject {
    @Published var isServerRunning = false
    @Published var serverStatus = "服务器已停止"
    
    private var listener: NWListener?
    private var connections: [NWConnection] = []
    private weak var clipboardManager: ClipboardManager?
    
    init(clipboardManager: ClipboardManager) {
        self.clipboardManager = clipboardManager
    }
    
    func startServer() {
        guard !isServerRunning else { return }
        
        do {
            let parameters = NWParameters.tcp
            parameters.allowLocalEndpointReuse = true
            
            listener = try NWListener(using: parameters, on: 5200)
            
            listener?.newConnectionHandler = { [weak self] connection in
                self?.handleNewConnection(connection)
            }
            
            listener?.stateUpdateHandler = { [weak self] state in
                DispatchQueue.main.async {
                    switch state {
                    case .ready:
                        self?.isServerRunning = true
                        self?.serverStatus = "服务器运行中 - http://localhost:5200"
                    case .failed(let error):
                        self?.isServerRunning = false
                        self?.serverStatus = "服务器启动失败: \(error.localizedDescription)"
                    case .cancelled:
                        self?.isServerRunning = false
                        self?.serverStatus = "服务器已停止"
                    default:
                        break
                    }
                }
            }
            
            listener?.start(queue: .global(qos: .userInitiated))
            
        } catch {
            DispatchQueue.main.async {
                self.serverStatus = "启动失败: \(error.localizedDescription)"
            }
        }
    }
    
    func stopServer() {
        listener?.cancel()
        connections.forEach { $0.cancel() }
        connections.removeAll()
        
        DispatchQueue.main.async {
            self.isServerRunning = false
            self.serverStatus = "服务器已停止"
        }
    }
    
    private func handleNewConnection(_ connection: NWConnection) {
        connections.append(connection)
        
        connection.stateUpdateHandler = { [weak self] state in
            if case .cancelled = state {
                self?.connections.removeAll { $0 === connection }
            }
        }
        
        connection.start(queue: .global(qos: .userInitiated))
        
        receiveRequest(from: connection)
    }
    
    private func receiveRequest(from connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 8192) { [weak self] data, _, isComplete, error in
            
            if let data = data, !data.isEmpty {
                let request = String(data: data, encoding: .utf8) ?? ""
                self?.handleHTTPRequest(request, connection: connection)
            }
            
            if isComplete {
                connection.cancel()
            } else if error == nil {
                self?.receiveRequest(from: connection)
            }
        }
    }
    
    private func handleHTTPRequest(_ request: String, connection: NWConnection) {
        let lines = request.components(separatedBy: "\r\n")
        guard let firstLine = lines.first else { return }
        
        let components = firstLine.components(separatedBy: " ")
        guard components.count >= 2 else { return }
        
        let method = components[0]
        let path = components[1]
        
        var response: String
        
        if method == "GET" && path == "/" {
            response = generateHTMLResponse()
        } else if method == "GET" && path == "/api/clipboard" {
            response = generateJSONResponse()
        } else {
            response = generate404Response()
        }
        
        let responseData = response.data(using: .utf8) ?? Data()
        connection.send(content: responseData, completion: .contentProcessed { _ in
            connection.cancel()
        })
    }
    
    private func generateHTMLResponse() -> String {
        guard let clipboardManager = clipboardManager else {
            return generateErrorResponse("剪切板管理器不可用")
        }
        
        let historyHTML = clipboardManager.history.enumerated().map { index, item in
            let timestamp = DateFormatter.localizedString(from: item.timestamp, dateStyle: .none, timeStyle: .medium)
            let escapedContent = item.content
                .replacingOccurrences(of: "&", with: "&amp;")
                .replacingOccurrences(of: "<", with: "&lt;")
                .replacingOccurrences(of: ">", with: "&gt;")
                .replacingOccurrences(of: "\"", with: "&quot;")
                .replacingOccurrences(of: "\n", with: "<br>")
            
            return """
            <div class="clipboard-item">
                <div class="item-header">
                    <span class="item-number">#\(index + 1)</span>
                    <span class="item-time">\(timestamp)</span>
                </div>
                <div class="item-content">\(escapedContent)</div>
            </div>
            """
        }.joined(separator: "\n")
        
        let html = """
        HTTP/1.1 200 OK\r
        Content-Type: text/html; charset=utf-8\r
        Connection: close\r
        \r
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>剪切板历史</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    padding: 20px;
                }
                h1 {
                    color: #333;
                    text-align: center;
                    margin-bottom: 30px;
                }
                .clipboard-item {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    margin-bottom: 15px;
                    padding: 15px;
                    background: #fafafa;
                }
                .item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                    font-size: 14px;
                    color: #666;
                }
                .item-number {
                    font-weight: bold;
                    color: #007AFF;
                }
                .item-content {
                    font-family: 'SF Mono', Monaco, monospace;
                    background: white;
                    padding: 10px;
                    border-radius: 4px;
                    border: 1px solid #e0e0e0;
                    white-space: pre-wrap;
                    word-break: break-word;
                }
                .empty-state {
                    text-align: center;
                    color: #666;
                    font-style: italic;
                    padding: 40px;
                }
                .refresh-btn {
                    display: block;
                    margin: 20px auto;
                    padding: 10px 20px;
                    background: #007AFF;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 16px;
                }
                .refresh-btn:hover {
                    background: #0056CC;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>剪切板历史</h1>
                <button class="refresh-btn" onclick="location.reload()">刷新</button>
                \(historyHTML.isEmpty ? "<div class='empty-state'>暂无剪切板记录</div>" : historyHTML)
            </div>
        </body>
        </html>
        """
        
        return html
    }
    
    private func generateJSONResponse() -> String {
        guard let clipboardManager = clipboardManager else {
            return generateErrorResponse("剪切板管理器不可用")
        }
        
        let jsonData = clipboardManager.history.map { item in
            [
                "id": item.id.uuidString,
                "content": item.content,
                "timestamp": ISO8601DateFormatter().string(from: item.timestamp)
            ]
        }
        
        do {
            let data = try JSONSerialization.data(withJSONObject: jsonData, options: .prettyPrinted)
            let jsonString = String(data: data, encoding: .utf8) ?? "[]"
            
            return """
            HTTP/1.1 200 OK\r
            Content-Type: application/json; charset=utf-8\r
            Connection: close\r
            \r
            \(jsonString)
            """
        } catch {
            return generateErrorResponse("JSON序列化失败")
        }
    }
    
    private func generate404Response() -> String {
        return """
        HTTP/1.1 404 Not Found\r
        Content-Type: text/html; charset=utf-8\r
        Connection: close\r
        \r
        <!DOCTYPE html>
        <html>
        <head><title>404 Not Found</title></head>
        <body>
            <h1>404 Not Found</h1>
            <p>请访问 <a href="/">主页</a> 查看剪切板内容</p>
        </body>
        </html>
        """
    }
    
    private func generateErrorResponse(_ message: String) -> String {
        return """
        HTTP/1.1 500 Internal Server Error\r
        Content-Type: text/html; charset=utf-8\r
        Connection: close\r
        \r
        <!DOCTYPE html>
        <html>
        <head><title>服务器错误</title></head>
        <body>
            <h1>服务器错误</h1>
            <p>\(message)</p>
        </body>
        </html>
        """
    }
    
    deinit {
        stopServer()
    }
}
