//
//  ContentView.swift
//  Paste
//
//  Created by 孙双 on 2025/1/28.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var clipboardManager = ClipboardManager()
    @StateObject private var serverManager: HTTPServerManager
    @State private var selectedItem: ClipboardItem?
    @State private var editedText: String = ""

    init() {
        let clipboardManager = ClipboardManager()
        self._clipboardManager = StateObject(wrappedValue: clipboardManager)
        self._serverManager = StateObject(wrappedValue: HTTPServerManager(clipboardManager: clipboardManager))
    }
    
    var body: some View {
        NavigationView {
            VStack {
                VStack(spacing: 10) {
                    HStack {
                        Button(clipboardManager.isRecording ? "停止记录" : "开始记录") {
                            clipboardManager.toggleRecording()
                        }
                        .foregroundColor(clipboardManager.isRecording ? .red : .green)

                        Spacer()

                        <PERSON><PERSON>("全部追加") {
                            // 按时间顺序从上到下追加所有记录
                            editedText = clipboardManager.history
                                .map { $0.content }
                                .joined(separator: "\n")
                        }
                        .disabled(clipboardManager.history.isEmpty)

                        But<PERSON>("清空记录") {
                            clipboardManager.clearHistory()
                            editedText = ""
                        }
                        .disabled(clipboardManager.history.isEmpty)
                    }

                    HStack {
                        Button(serverManager.isServerRunning ? "停止服务器" : "启动服务器") {
                            if serverManager.isServerRunning {
                                serverManager.stopServer()
                            } else {
                                serverManager.startServer()
                            }
                        }
                        .foregroundColor(serverManager.isServerRunning ? .red : .blue)

                        if serverManager.isServerRunning {
                            Button("打开浏览器") {
                                if let url = URL(string: "http://localhost:5200") {
                                    NSWorkspace.shared.open(url)
                                }
                            }
                            .foregroundColor(.blue)
                        }

                        Spacer()

                        Text(serverManager.serverStatus)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()

                List {
                    ForEach(clipboardManager.history) { item in
                        VStack(alignment: .leading) {
                            Text(item.content)
                                .lineLimit(2)
                            Text(item.timestamp, style: .time)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            if editedText.isEmpty {
                                editedText = item.content
                            } else {
                                editedText += " " + item.content
                            }
                            selectedItem = item
                        }
                        .contextMenu {
                            Button("复制") {
                                NSPasteboard.general.clearContents()
                                NSPasteboard.general.setString(item.content, forType: .string)
                            }
                        }
                    }
                }
            }
            .navigationTitle("剪贴板历史")
            .frame(minWidth: 300)
            
            // 右侧详细内容视图
            ScrollView {
                VStack {
                    TextEditor(text: $editedText)
                        .font(.system(.body))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding()
                    
                    HStack {
                        Button("清空") {
                            editedText = ""
                        }
                        .disabled(editedText.isEmpty)
                        
                        Spacer()
                        
                        Button("复制") {
                            NSPasteboard.general.clearContents()
                            NSPasteboard.general.setString(editedText, forType: .string)
                        }
                        .disabled(editedText.isEmpty)
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
            }
            .frame(minWidth: 400)
        }
    }
}

#Preview {
    ContentView()
}
